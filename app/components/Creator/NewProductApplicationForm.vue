<template>
  <!-- Add scrollable container for form overflow -->
  <div class="h-full overflow-y-auto">
    <UForm :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
    <UFormField :label="$t('global.product_name')" name="name">
      <UInput v-model="state.name"  class="w-full" />
    </UFormField>

    <UFormField :label="$t('global.product_description')" name="description">
      <UTextarea v-model="state.description"  class="w-full"/>
    </UFormField>

    <UFormField :label="$t('global.product_category')" name="category">
      <USelect v-model="state.category" :items="localizedProductCategoryItems" class="w-full" />
    </UFormField>

    <UFormField :label="$t('global.product_application_type')" name="application-type">
      <USelect v-model="state.applicationType" :items="applicationCategoryItems" class="w-full"/>
    </UFormField>

    <!-- Product Images Dropzone -->
    <ProductImageDropzone
      v-model="state.images!"
      :product-name="state.name"
      :max-images="5"
    />

    <div class="grid grid-cols-2 gap-4">
      <UFormField :label="$t('global.price')" name="price">
        <UButtonGroup>
          <USelect v-model="state.currency" :items="CURRENCIES" class="w-24" />
          <UInput type="number" v-model="state.price" />
        </UButtonGroup>
      </UFormField>

      <UFormField :label="$t('global.total_stock')" name="total-stock">
        <UInput v-model="state.totalStock" type="number" />
      </UFormField>
    </div>

    <UButton type="submit">
      Submit
    </UButton>
  </UForm>
  </div>
</template>

<script lang="ts" setup>
import z from 'zod/v4';
import type { FormSubmitEvent } from '@nuxt/ui';
import { UTextarea } from '#components';
const { t , locale} = useI18n();
const { useDirectusFetch } = useDirectus();
const route = useRoute('creator-ip-id___en');

const emit = defineEmits(['close']);

const schema = z.object({
  name: z.string().min(3, t('global.add_new_product_tooltip')),
  description: z.string().min(10, t('global.product_description_tooltip')),
  category: z.number().min(1, t('global.product_category_tooltip')),
  applicationType: z.number().min(1, t('global.product_application_type')),
  images: z.array(z.string()).min(1, t('global.at_least_one_image_required')),
  price: z.number().min(1, t('global.price_tooltip')),
  currency: z.string().min(1, t('global.currency')),
  totalStock: z.number().min(1, t('global.total_stock_tooltip')),
});

type Schema = z.output<typeof schema>

const state = reactive<Partial<Schema>>({
  name: '',
  description: '',
  category: 0,
  applicationType: 1,
  images: [],
  price: 0,
  currency: 'USD',
  totalStock: 10,
})

const toast = useToast()

const { data: productCategories } = useDirectusFetch<ProductCategories[]>('/items/product_categories', {
  key: 'product-categories',
  params: {
    fields: ['id', 'translations.*'],
  },
});

const localizedProductCategoryItems = computed(() => {
  if (!productCategories.value) return [];
  return productCategories.value.map((item) => ({
    label: item.translations[localeToIndex(locale.value)].name ?? '',
    value: item.id,
  }));
});

const { data: applicationCategory } = useDirectusFetch<ProductApplicationCategory[]>('/items/product_application_category',{
  key: 'product-application-category',
  params: {
    fields: ['id', 'name']
  }
});

const applicationCategoryItems = computed(() => {
  return (applicationCategory.value ?? []).map((item) => ({
    label: item.name ?? '',
    value: item.id ?? '',
  }));
});

async function onSubmit(event: FormSubmitEvent<Schema>) {
  try {
    // Use the new server endpoint for product creation
    const response = await $fetch('/api/creator/products/create', {
      method: 'POST',
      body: {
        ...event.data,
        ip: parseInt(route.params.id, 10),
        locale: locale.value,
      }
    });

    if (response.success) {
      toast.add({
        title: t('global.success'),
        description: t('global.product_application_success'),
        color: 'success'
      });
      emit('close');
    }
  } catch (error) {
    console.error('Failed to submit application:', error);
    toast.add({
      title: t('global.error'),
      description: t('global.unexpected_error'),
      color: 'error',
    });
  }
}

</script>