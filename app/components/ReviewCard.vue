<template>
  <div class="flex gap-4 p-4">
    <!-- Avatar -->
    <div v-if="(review?.user_created as DirectusUsers)?.avatar">
      <UAvatar
        size="xl"
        class="col-span-1 h-16 w-16"
        :src="directusAssetsUrl((review?.user_created as DirectusUsers)?.avatar as string)"
      />
    </div>
    <div v-else>
      <UIInitialAvatar :name shape="circle" class="col-span-2 h-20 w-20" />
    </div>
    <!-- Review Details -->
    <div class="col-span-8 flex flex-col justify-center gap-2">
      <h2 class="font-bold">
        {{ name ?? 'Anonymous' }}
        <span class="text-xs text-neutral-500">{{ relativeDate }}</span>
      </h2>
      <vue3-star-ratings v-model="rating" starColor="white" :starSize="14" disableClick class="-mt-1" />
      <p class="text-sm text-neutral-400">{{ review.text }}</p>

      <!-- Thumbs up and down with counter-->
      <!-- Implementation missing -->
      <!-- <div class="flex flex-row items-center gap-2">
        <UButton icon="heroicons-outline:thumb-up" variant="ghost" size="xs" />
        <p class="text-xs text-neutral-500">0</p>
        <UButton icon="heroicons-outline:thumb-down" variant="ghost" size="xs" />
        <p class="text-xs text-neutral-500">0</p>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import vue3StarRatings from 'vue3-star-ratings';
import { format } from 'date-fns';
const { directusAssetsUrl } = useDirectus();
const props = defineProps<{ review: Review }>();

const rating = ref(props.review.rating ?? 0);

const relativeDate = computed(() => {
  return format(new Date(props.review.date_created!), 'yyyy-MM-dd');
});

const name = computed(() => {
  return `${(props.review.user_created as DirectusUsers)?.first_name} ${(props.review.user_created as DirectusUsers)?.last_name}`;
});
</script>
