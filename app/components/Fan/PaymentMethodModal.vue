<template>
  <UModal v-model:open="isOpen">
    <slot/>
    <template #body>
      <div class="p-4 -mt-3 md:-mt-6">
        <p class="-mt-4 text-sm text-muted mb-4">{{ $t('global.all_fields_are_required') }}</p>

        <!-- Add Mode: Stripe Card Elements -->
        <div v-if="props.mode === 'add'" class="mb-4 space-y-4">
          <!-- Card Number -->
          <div>
            <label class="block text-sm font-medium mb-2">{{ $t('global.card_number') }}</label>
            <div id="card-number-element" :class="cardNumberClass" class="h-[2.75rem]">
              <!-- Card number element will mount here -->
            </div>
            <!-- Card Brand Logos Row -->
            <div class="flex gap-1 mb-2">
              <img
                v-for="brand in cardBrands"
                :key="brand.name"
                :src="brand.logo"
                :alt="brand.name"
                :class="[
                  'w-6 h-6 transition-opacity duration-200',
                  detectedCardBrand === brand.name ? 'opacity-100' : 'opacity-20'
                ]"
              />
              <!-- Default logo for other supported brands -->
              <img
                :src="defaultCardLogo"
                :alt="t('global.other_supported_cards')"
                :class="[
                  'w-6 h-6 transition-opacity duration-200',
                  detectedCardBrand && !isMajorBrand(detectedCardBrand) ? 'opacity-100' : 'opacity-20'
                ]"
              />
            </div>
          </div>

          <!-- Cardholder Name (Regular Input) -->
          <div>
            <label class="block text-sm font-medium mb-2">{{ $t('global.cardholder_name') }}</label>
            <UInput
              v-model="cardholderName"
              type="text"
              size="lg"
              placeholder="John Doe"
              class="w-full"
              :ui="{
                base:'p-3 bg-[#22202C]'
              }"
            />
          </div>

          <!-- Expiry and CVC -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">{{ $t('global.expiry_date') }}</label>
              <div id="card-expiry-element" class="p-3 border border-gray-300 rounded-lg bg-white dark:bg-[#22202C] dark:border-gray-600 h-[2.75rem]">
                <!-- Card expiry element will mount here -->
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">{{ $t('global.cvc') }}</label>
              <div id="card-cvc-element" class="p-3 border border-gray-300 rounded-lg bg-white dark:bg-[#22202C] dark:border-gray-600 h-[2.75rem]">
                <!-- Card CVC element will mount here -->
              </div>
            </div>
          </div>

          <!-- Error display -->
          <div id="card-errors" role="alert" class="text-red-500 text-sm"></div>
        </div>

        <!-- Edit Mode: Read-only card info + editable name -->
        <div v-else-if="props.mode === 'edit'" class="mb-4 space-y-4">
          <!-- Card Details (Read-only) -->
          <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
            <label class="block text-sm font-medium mb-2">{{ $t('global.card_details') }}</label>
            <div class="flex items-center gap-3">
              <!-- Card brand logo -->
              <img
                :src="getCardBrandLogo(props.paymentMethod?.card?.brand)"
                :alt="getCardBrandDisplay(props.paymentMethod?.card?.brand)"
                class="w-8 h-5"
              />

              <!-- Card info -->
              <div>
                <p class="text-sm font-medium">
                  {{ getCardBrandDisplay(props.paymentMethod?.card?.brand) }} •••• {{ props.paymentMethod?.card?.last4 }}
                </p>
                <p class="text-xs text-muted">
                  {{ $t('global.expires') }} {{ props.paymentMethod?.card?.exp_month }}/{{ props.paymentMethod?.card?.exp_year }}
                </p>
              </div>
            </div>
          </div>

          <!-- Cardholder Name (Editable) -->
          <div>
            <label class="block text-sm font-medium mb-2">{{ $t('global.cardholder_name') }}</label>
            <UInput
              v-model="cardholderName"
              type="text"
              size="lg"
              placeholder="John Doe"
              class="w-full"
              :ui="{
                base:'p-3 bg-[#22202C]'
              }"
            />
          </div>

          <!-- Error display -->
          <div id="card-errors" role="alert" class="text-red-500 text-sm"></div>
        </div>

        <!-- Set as Default Checkbox (for both add and edit modes) -->
        <div class="mb-4">
          <div class="flex flex-row gap-2">
            <UCheckbox v-model="setAsDefault" />
            <span>{{ $t('global.set_as_default_payment_method') }}</span>
          </div>
        </div>

        <!-- Submit Button -->
        <UButton
          @click="handleSubmit"
          color="primary"
          size="lg"
          class="w-full justify-center rounded-full mb-4"
          :loading="isLoading"
          :disabled="props.mode === 'add' ? !isCardComplete : false"
        >
          {{ props.mode === 'add' ? $t('global.add_payment_method') : $t('global.update_payment_method') }}
        </UButton>

        <!-- All transactions are secured and encrypted -->
        <div class="flex justify-center gap-2">
          <UIcon name="octicon:shield-check-16" class="w-4 h-4" />
          <p class="text-xs text-muted text-center">{{ $t('global.all_transactions_are_secured_and_encrypted') }}</p>
        </div>

        <!-- Debug Info -->
        <DevOnly>
          <div class="mt-4 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs">
            <p>Mode: {{ props.mode }}</p>
            <p>isLoading: {{ isLoading }}</p>
            <p>isCardComplete: {{ isCardComplete }}</p>
            <p>cardholderName: "{{ cardholderName }}"</p>
            <p>paymentMethod.id: {{ props.paymentMethod?.id }}</p>
            <p>detectedCardBrand: "{{ detectedCardBrand }}"</p>
            <p>cardBrands length: {{ cardBrands.length }}</p>
            <p>defaultCardLogo: {{ defaultCardLogo }}</p>
            <UButton @click="testClick" size="xs" class="mt-2">Test Click</UButton>
          </div>
        </DevOnly>
      </div>
    </template>
  </UModal>
</template>

<script lang="ts" setup>
const props = defineProps<{
  mode: 'add' | 'edit';
  paymentMethod?: any;
}>();
const emit = defineEmits(['submit']);

const { t, locale } = useI18n();
const isOpen = ref(false);
const isLoading = ref(false);
const isCardComplete = ref(false);
// Initialize setAsDefault based on mode and current default status
// For edit mode, we'll default to false (user can check if they want to set as default)
// For add mode, we'll default to true (new cards are usually set as default)
const setAsDefault = ref(props.mode === 'add' ? true : false);
const cardholderName = ref(props.paymentMethod?.billing_details?.name || '');
const detectedCardBrand = ref('');

// Import utility functions from payments utility
const { getMajorCardBrands, getCardBrandDisplay, getCardBrandLogo } = await import('../../../utils/payments');
const cardBrands = getMajorCardBrands();

// Helper function to check if a brand is in the major brands list
const isMajorBrand = (brand: string): boolean => {
  const majorBrandNames = cardBrands.map(b => b.name);
  return majorBrandNames.includes(brand);
};

// Computed property for default card logo path
const defaultCardLogo = computed(() => '/images/card-default.svg');

// Validation states
const cardNumberComplete = ref(false);
const cardExpiryComplete = ref(false);
const cardCvcComplete = ref(false);
const showValidation = ref(false);

// Computed validation classes
const cardNumberClass = computed(() =>
  showValidation.value && !cardNumberComplete.value
    ? 'p-3 border border-red-500 rounded-lg bg-white dark:bg-[#22202C]'
    : 'p-3 border border-gray-300 rounded-lg bg-white dark:bg-[#22202C] dark:border-gray-600'
);

const cardExpiryClass = computed(() =>
  showValidation.value && !cardExpiryComplete.value
    ? 'p-3 border border-red-500 rounded-lg bg-white dark:bg-[#22202C]'
    : 'p-3 border border-gray-300 rounded-lg bg-white dark:bg-[#22202C] dark:border-gray-600'
);

const cardCvcClass = computed(() =>
  showValidation.value && !cardCvcComplete.value
    ? 'p-3 border border-red-500 rounded-lg bg-white dark:bg-[#22202C]'
    : 'p-3 border border-gray-300 rounded-lg bg-white dark:bg-[#22202C] dark:border-gray-600'
);

const cardholderNameClass = computed(() =>
  showValidation.value && !cardholderName.value.trim()
    ? 'border-red-500'
    : ''
);



// Initialize Stripe at component root
const { stripe } = useClientStripe();

// Stripe Elements variables
let elements: any = null;
let cardNumberElement: any = null;
let cardExpiryElement: any = null;
let cardCvcElement: any = null;

// Initialize Stripe Elements when modal opens, stripe is ready, or locale changes (only for add mode)
watch([isOpen, stripe, locale], async ([newIsOpen, stripeInstance]) => {
  if (newIsOpen && stripeInstance && props.mode === 'add') {
    await initializeStripeElements();
  }
});

const initializeStripeElements = async () => {
  try {
    if (!stripe.value) {
      console.error('Stripe not loaded');
      return;
    }

    // Clean up existing elements if they exist
    if (cardNumberElement) {
      cardNumberElement.destroy();
    }
    if (cardExpiryElement) {
      cardExpiryElement.destroy();
    }
    if (cardCvcElement) {
      cardCvcElement.destroy();
    }

    // Create Setup Intent
    const setupIntent = await $fetch('/api/payment/setup-intent/create', {
      method: 'POST'
    });

    if (!setupIntent.clientSecret) {
      console.error('Failed to create setup intent');
      return;
    }

    // Map i18n locale to Stripe locale
    const getStripeLocale = (i18nLocale: string) => {
      switch (i18nLocale) {
        case 'jp':
          return 'ja' as const; // Japanese
        case 'kr':
          return 'ko' as const; // Korean
        case 'en':
        default:
          return 'en' as const; // English (default)
      }
    };

    // Create Elements instance with current locale
    elements = stripe.value.elements({
      locale: getStripeLocale(locale.value)
    });

    // Common style for all elements
    const elementStyle = {
      base: {
        fontSize: '16px',
        color: '#ffffff', // White text for user input
        '::placeholder': {
          color: '#424770', // Keep placeholder color as is
        },
      },
    };

    // Create individual card elements
    cardNumberElement = elements.create('cardNumber', { style: elementStyle });
    cardExpiryElement = elements.create('cardExpiry', { style: elementStyle });
    cardCvcElement = elements.create('cardCvc', { style: elementStyle });

    // Mount elements to their containers
    cardNumberElement.mount('#card-number-element');
    cardExpiryElement.mount('#card-expiry-element');
    cardCvcElement.mount('#card-cvc-element');

    // Listen for changes on card number element (with brand detection)
    cardNumberElement.on('change', (event: any) => {
      const displayError = document.getElementById('card-errors');
      if (event.error) {
        displayError!.textContent = event.error.message;
        isCardComplete.value = false;
      } else {
        displayError!.textContent = '';
        isCardComplete.value = event.complete;
      }

      // Detect card brand from Stripe's brand detection
      if (event.brand) {
        detectedCardBrand.value = event.brand;
      } else {
        detectedCardBrand.value = '';
      }
    });

    // Listen for changes on other elements
    const handleElementChange = (event: any) => {
      const displayError = document.getElementById('card-errors');
      if (event.error) {
        displayError!.textContent = event.error.message;
        isCardComplete.value = false;
      } else {
        displayError!.textContent = '';
        isCardComplete.value = event.complete;
      }
    };

    cardExpiryElement.on('change', handleElementChange);
    cardCvcElement.on('change', handleElementChange);

  } catch (error) {
    console.error('Error initializing Stripe Elements:', error);
  }
};

// Test function for debugging
const testClick = () => {
  console.log('🔥 TEST CLICK WORKS!');
  console.log('Mode:', props.mode);
  console.log('Cardholder name:', cardholderName.value);
  console.log('Payment method:', props.paymentMethod);
};

const handleSubmit = async () => {
  isLoading.value = true;

  try {
    if (props.mode === 'edit') {
      // Edit mode: Update existing payment method billing details only
      if (!cardholderName.value.trim()) {
        const displayError = document.getElementById('card-errors');
        displayError!.textContent = t('global.please_fill_all_required_fields');
        isLoading.value = false;
        return;
      }

      // Call API to update payment method billing details
      await $fetch(`/api/payment/method/${props.paymentMethod.id}`, {
        method: 'PATCH',
        body: {
          billing_details: {
            name: cardholderName.value.trim()
          }
        }
      });

      // Set as default if requested
      if (setAsDefault.value) {
        try {
          await $fetch(`/api/payment/method/${props.paymentMethod.id}/default`, {
            method: 'PATCH'
          });
          // Small delay to ensure Stripe has processed the change
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (defaultError) {
          console.error('Error setting as default:', defaultError);
        }
      }

      // Emit success with payment method ID
      emit('submit', props.paymentMethod.id);
      isLoading.value = false;
      isOpen.value = false;
      return;
    }

    // Add mode: Create new payment method with Stripe Elements
    if (!stripe.value || !cardNumberElement) {
      console.error('Stripe not initialized');
      return;
    }

    // Get the Setup Intent client secret
    const setupIntent = await $fetch('/api/payment/setup-intent/create', {
      method: 'POST'
    });

    if (!setupIntent.clientSecret) {
      console.error('Failed to create setup intent');
      return;
    }

    // Confirm the setup intent with card number element and billing details
    const { error, setupIntent: confirmedSetupIntent } = await stripe.value.confirmCardSetup(
      setupIntent.clientSecret,
      {
        payment_method: {
          card: cardNumberElement,
          billing_details: {
            name: cardholderName.value || undefined,
          }
        }
      }
    );

    if (error) {
      console.error('Error confirming setup:', error);
      const displayError = document.getElementById('card-errors');
      displayError!.textContent = error.message || 'An error occurred';
    } else {
      // Setup successful
      console.log('✅ Setup Intent confirmed:', confirmedSetupIntent);
      console.log('💳 New payment method ID:', confirmedSetupIntent.payment_method);

      // Set as default if requested
      if (setAsDefault.value && confirmedSetupIntent.payment_method) {
        console.log('🎯 Setting as default payment method...');
        try {
          await $fetch(`/api/payment/method/${confirmedSetupIntent.payment_method}/default`, {
            method: 'PATCH'
          });
          console.log('✅ Successfully set as default');
        } catch (defaultError) {
          console.error('❌ Error setting as default:', defaultError);
        }
      }

      console.log('📤 Emitting submit event with payment method ID:', confirmedSetupIntent.payment_method);
      // Emit success with payment method ID
      emit('submit', confirmedSetupIntent.payment_method);
      isOpen.value = false;
    }
  } catch (error) {
    console.error('Error submitting payment method:', error);
  } finally {
    isLoading.value = false;
  }
};
</script>
