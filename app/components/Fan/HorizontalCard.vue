<template>
  <div
    class="hover-brightness grid w-[85vw] cursor-pointer grid-cols-[9fr_10fr] overflow-hidden rounded-md border border-[#3F3D47] lg:h-64 lg:w-[640px]"
  >
    <NuxtImg
      :src="item.image"
      class="h-full w-full rounded-tl-md rounded-bl-md object-cover blur-none transition-all duration-100"
      loading="lazy"
      placeholder
      placeholder-class="blur-xs"
    />
    <div class="flex h-full flex-col items-start justify-between gap-4 rounded-tr-md rounded-br-md bg-[#22202CB2] p-4">
      <div class="flex flex-col gap-2">
        <h3 v-if="title" class="text-lg font-semibold">{{ title }}</h3>
        <p v-if="endDate" class="-mt-2 line-clamp-1 text-xs text-neutral-400">Offer ends {{ endDate }}</p>
        <p v-if="description" class="line-clamp-3 text-xs text-neutral-400 lg:line-clamp-5">{{ description }}</p>
        <div class="flex gap-2">
          <p v-for="tag in item.tags" :key="tag" class="text-xs text-neutral-500">#{{ tag }}</p>
        </div>
      </div>
      <div class="flex items-baseline gap-1">
        <p class="text-primary-500 text-2xl font-bold">{{ priceFormatter(item.discountPrice) }}</p>
        <p class="text-xs text-neutral-400 line-through">{{ priceFormatter(item.price) }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { priceFormatter } from '~/utils/formatter';

type PromotionItem = {
  image: string;
  title1?: string;
  title2?: string;
  title3?: string;
  description1?: string;
  description2?: string;
  description3?: string;
  tags?: string[];
  price: number;
  discountPrice: number;
};

const props = defineProps<{
  item: PromotionItem;
  title?: string;
  description?: string;
  endDate?: string;
}>();
</script>
