<template>
  <UCarousel
    :align="'start'"
    loop
    wheel-gestures
    v-slot="{ item }"
    dots
    :items="items"
    class="mx-auto w-full"
    :ui="{
      dots: 'scale-50',
      item: 'basis-auto',
    }"
  >
    <FanHorizontalCard
      :item="item"
      :title="getTitle(item)"
      :description="getDescription(item)"
      :end-date="'26 Sep 2.00pm'"
    />
  </UCarousel>
</template>

<script lang="ts" setup>
const { locale } = useI18n();

type HeroCarouselItem = {
  image: string;
  title1?: string;
  title2?: string;
  title3?: string;
  description1?: string;
  description2?: string;
  description3?: string;
  tags?: string[];
  price: number;
  discountPrice: number;
};
const items: HeroCarouselItem[] = [
  {
    image:
      'https://images.unsplash.com/photo-1621478374422-35206faeddfb?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    title1: 'English Title',
    title2: 'Japanese Title',
    title3: 'Korean Title',
    description1:
      'Lorem ipsum dolor sit amet consectetur. Vitae volutpat sem sit massa vel nullam tempor velit quisque. Dictum convallis arcu id pellentesque eget. Condimentum tellus nisl nulla leo urna consectetur pharetra. Ut elit dui sed convallis est. Lacus non morbi amet id posuere faucibus hendrerit. Id dui magna ullamcorper purus cursus dolor felis vitae accumsan. Morbi eu varius id proin aliquet id. Turpis habitasse sem feugiat turpis. Condimentum faucibus sollicitudin habitant sed volutpat risus vel ',
    description2:
      'Lorem ipsum dolor sit amet consectetur. Vitae volutpat sem sit massa vel nullam tempor velit quisque. Dictum convallis arcu id pellentesque eget. Condimentum tellus nisl nulla leo urna consectetur pharetra. Ut elit dui sed convallis est. Lacus non morbi amet id posuere faucibus hendrerit. Id dui magna ullamcorper purus cursus dolor felis vitae accumsan. Morbi eu varius id proin aliquet id. Turpis habitasse sem feugiat turpis. Condimentum faucibus sollicitudin habitant sed volutpat risus vel ',
    description3:
      'Lorem ipsum dolor sit amet consectetur. Vitae volutpat sem sit massa vel nullam tempor velit quisque. Dictum convallis arcu id pellentesque eget. Condimentum tellus nisl nulla leo urna consectetur pharetra. Ut elit dui sed convallis est. Lacus non morbi amet id posuere faucibus hendrerit. Id dui magna ullamcorper purus cursus dolor felis vitae accumsan. Morbi eu varius id proin aliquet id. Turpis habitasse sem feugiat turpis. Condimentum faucibus sollicitudin habitant sed volutpat risus vel ',
    tags: ['Disney', 'Frozen', 'Elsa'],
    price: 66.0,
    discountPrice: 50.0,
  },
  {
    image:
      'https://images.unsplash.com/photo-1578632749014-ca77efd052eb?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    title1: 'English Title',
    title2: 'Japanese Title',
    title3: 'Korean Title',
    description1:
      'Lorem ipsum dolor sit amet consectetur. Vitae volutpat sem sit massa vel nullam tempor velit quisque. Dictum convallis arcu id pellentesque eget. Condimentum tellus nisl nulla leo urna consectetur pharetra. Ut elit dui sed convallis est. Lacus non morbi amet id posuere faucibus hendrerit. Id dui magna ullamcorper purus cursus dolor felis vitae accumsan. Morbi eu varius id proin aliquet id. Turpis habitasse sem feugiat turpis. Condimentum faucibus sollicitudin habitant sed volutpat risus vel ',
    description2:
      'Lorem ipsum dolor sit amet consectetur. Vitae volutpat sem sit massa vel nullam tempor velit quisque. Dictum convallis arcu id pellentesque eget. Condimentum tellus nisl nulla leo urna consectetur pharetra. Ut elit dui sed convallis est. Lacus non morbi amet id posuere faucibus hendrerit. Id dui magna ullamcorper purus cursus dolor felis vitae accumsan. Morbi eu varius id proin aliquet id. Turpis habitasse sem feugiat turpis. Condimentum faucibus sollicitudin habitant sed volutpat risus vel ',
    description3:
      'Lorem ipsum dolor sit amet consectetur. Vitae volutpat sem sit massa vel nullam tempor velit quisque. Dictum convallis arcu id pellentesque eget. Condimentum tellus nisl nulla leo urna consectetur pharetra. Ut elit dui sed convallis est. Lacus non morbi amet id posuere faucibus hendrerit. Id dui magna ullamcorper purus cursus dolor felis vitae accumsan. Morbi eu varius id proin aliquet id. Turpis habitasse sem feugiat turpis. Condimentum faucibus sollicitudin habitant sed volutpat risus vel ',
    tags: ['New', 'Sale', 'Hot'],
    price: 66.0,
    discountPrice: 50.0,
  },
];

const getTitle = (item: HeroCarouselItem) => {
  let title = '';

  // Try to get title for the requested locale
  if (locale.value === 'en') title = item.title1 || '';
  else if (locale.value === 'ja') title = item.title2 || '';
  else if (locale.value === 'ko') title = item.title3 || '';
  else title = item.title1 || ''; // default to English for unknown locales

  // If the title is empty, try to find a non-empty one
  if (!title || title.trim() === '') {
    title = item.title1 || item.title2 || item.title3 || '';
  }

  return title;
};

const getDescription = (item: HeroCarouselItem) => {
  let description = '';

  // Try to get description for the requested locale
  if (locale.value === 'en') description = item.description1 || '';
  else if (locale.value === 'ja') description = item.description2 || '';
  else if (locale.value === 'ko') description = item.description3 || '';
  else description = item.description1 || ''; // default to English for unknown locales

  // If the description is empty, try to find a non-empty one
  if (!description || description.trim() === '') {
    description = item.description1 || item.description2 || item.description3 || '';
  }

  return description;
};
</script>
