<template>
  <div class="p-6 bg-gray-100 dark:bg-gray-800 rounded-lg">
    <h2 class="text-xl font-bold mb-4">Translation Example</h2>

    <!-- Language Switcher -->
    <div class="mb-4">
      <LangSwitcher />
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
        Current locale: <code>{{ locale }}</code>
      </p>
    </div>

    <!-- Example with mock product data -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">Product Translation Example:</h3>

      <div class="bg-white dark:bg-gray-700 p-4 rounded border">
        <h4 class="font-medium">Product Name:</h4>
        <p class="text-lg">{{ productName }}</p>

        <h4 class="font-medium mt-2">Product Description:</h4>
        <p class="text-sm text-gray-600 dark:text-gray-300">{{ productDescription }}</p>
      </div>

      <!-- Show raw translation data for debugging -->
      <details class="mt-4">
        <summary class="cursor-pointer text-sm text-gray-500">Show raw translation data</summary>
        <pre class="mt-2 p-2 bg-gray-100 dark:bg-gray-900 rounded text-xs overflow-auto">{{ JSON.stringify(mockProductTranslations, null, 2) }}</pre>
      </details>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProductsTranslations } from '../../shared/types/directus';

const { locale } = useI18n();

// Mock product translation data for demonstration
const mockProductTranslations: ProductsTranslations[] = [
  {
    id: 1,
    products_id: 1,
    languages_id: 1, // English
    name: 'Awesome Figure',
    description: 'A high-quality collectible figure with amazing details and craftsmanship.',
  },
  {
    id: 2,
    products_id: 1,
    languages_id: 2, // Japanese
    name: 'すごいフィギュア',
    description: '素晴らしいディテールと職人技を持つ高品質なコレクタブルフィギュア。',
  },
  {
    id: 3,
    products_id: 1,
    languages_id: 3, // Korean
    name: '멋진 피규어',
    description: '놀라운 디테일과 장인정신이 담긴 고품질 수집용 피규어입니다.',
  },
];

// Use the translation utilities
const productName = useTranslatedName(mockProductTranslations);
const productDescription = useTranslatedDescription(mockProductTranslations);
</script>
</template>
