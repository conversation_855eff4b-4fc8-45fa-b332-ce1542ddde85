<script setup lang="ts">
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'

// Props for start and end dates
interface Props {
  startDate?: CalendarDate | null
  endDate?: CalendarDate | null
}

const props = withDefaults(defineProps<Props>(), {
  startDate: null,
  endDate: null
})

// Emits for date changes
const emit = defineEmits<{
  'update:startDate': [date: CalendarDate | null]
  'update:endDate': [date: CalendarDate | null]
  'dateRangeChange': [range: { start: CalendarDate | null, end: CalendarDate | null }]
}>()

const df = new DateFormatter('ja-JP', {
  dateStyle: 'medium'
})

// Reactive model value that syncs with props
const modelValue = computed({
  get: () => ({
    start: props.startDate || undefined,
    end: props.endDate || undefined
  }),
  set: (value) => {
    // Emit individual date updates
    emit('update:startDate', value.start || null)
    emit('update:endDate', value.end || null)
    // Emit combined range change
    emit('dateRangeChange', { start: value.start || null, end: value.end || null })
  }
})
</script>

<template>
  <UPopover>
    <UButton color="neutral" variant="subtle" size="sm" trailing-icon="i-lucide-calendar" class="px-4 font-thin">
      <template v-if="modelValue.start">
        <template v-if="modelValue.end">
          {{ df.format(modelValue.start.toDate(getLocalTimeZone())) }} - {{ df.format(modelValue.end.toDate(getLocalTimeZone())) }}
        </template>

        <template v-else>
          {{ df.format(modelValue.start.toDate(getLocalTimeZone())) }}
        </template>
      </template>
      <template v-else>
        {{  $t('global.pick_a_date') }}
      </template>
    </UButton>

    <template #content>
      <UCalendar v-model="modelValue" class="p-2" :number-of-months="2" range />
    </template>
  </UPopover>
</template>

