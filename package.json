{"name": "ipgo-nuxt-app", "private": true, "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "dev:cf": "NITRO_PRESET=cloudflare-module nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "add:t": "node i18n/add-i18n-entry.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:restart": "pm2 restart ipgo-nuxt", "pm2:stop": "pm2 stop ipgo-nuxt", "pm2:logs": "pm2 logs ipgo-nuxt", "pm2:monit": "pm2 monit"}, "dependencies": {"@aws-sdk/client-ses": "^3.817.0"}, "devDependencies": {"@formkit/auto-animate": "^0.8.2", "@nuxt/image": "1.10.0", "@nuxt/ui-pro": "^3.1.1", "@nuxtjs/i18n": "9.5.3", "@types/date-fns": "^2.6.3", "@unlok-co/nuxt-stripe": "5.0.0", "@vueuse/nuxt": "^13.1.0", "date-fns": "^4.1.0", "nuxt": "3.17.3", "nuxt-charts": "^0.1.8", "nuxt-easy-lightbox": "1.0.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "transition-style": "^0.1.4", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-star-ratings": "^3.0.5", "zod": "^3.24.4"}}